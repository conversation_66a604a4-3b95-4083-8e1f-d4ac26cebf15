<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class NotificationTemplate extends Model
{
    protected $fillable = ['key', 'type', 'is_active', 'placeholders'];
    
    protected $casts = [
        'placeholders' => 'array'
    ];

    public function translations()
    {
        return $this->hasMany(NotificationTemplateTranslation::class);
    }

    public function translate($locale = null)
    {
        $locale = $locale ?: app()->getLocale();
        return $this->translations()->where('locale', $locale)->first();
    }

    public function getTranslation($locale, $field)
    {
        $translation = $this->translate($locale);
        return $translation ? $translation->$field : $this->translate('en')->$field ?? '';
    }

    public function getPlaceholdersList()
    {
        return $this->placeholders ?? [];
    }
}
