<?php

namespace App\Http\Controllers;

use App\Models\NotificationTemplate;
use App\Models\NotificationTemplateTranslation;
use Illuminate\Http\Request;

class NotificationTemplateController extends Controller
{
    public function index()
    {
        $templates = NotificationTemplate::with('translations')->get();
        return view('dashboard.notification-templates.index', compact('templates'));
    }

    public function create()
    {
        return view('dashboard.notification-templates.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'key' => 'required|unique:notification_templates,key',
            'placeholders' => 'nullable|array',
            'translations.en.title' => 'required',
            'translations.en.message' => 'required',
            'translations.es.title' => 'required',
            'translations.es.message' => 'required',
        ]);

        $template = NotificationTemplate::create([
            'key' => $request->key,
            'type' => $request->type ?? 'custom',
            'is_active' => true,
            'placeholders' => $request->placeholders ?? []
        ]);

        foreach ($request->translations as $locale => $translation) {
            NotificationTemplateTranslation::create([
                'notification_template_id' => $template->id,
                'locale' => $locale,
                'title' => $translation['title'],
                'message' => $translation['message']
            ]);
        }

        return redirect()->route('notification-templates.index')
            ->with('success', 'Notification template created successfully');
    }

    public function edit($id)
    {
        $template = NotificationTemplate::with('translations')->findOrFail($id);
        return view('dashboard.notification-templates.edit', compact('template'));
    }

    public function update(Request $request, $id)
    {
        $template = NotificationTemplate::findOrFail($id);
        
        $request->validate([
            'translations.en.title' => 'required',
            'translations.en.message' => 'required',
            'translations.es.title' => 'required',
            'translations.es.message' => 'required',
        ]);

        foreach ($request->translations as $locale => $translation) {
            NotificationTemplateTranslation::updateOrCreate(
                [
                    'notification_template_id' => $template->id,
                    'locale' => $locale
                ],
                [
                    'title' => $translation['title'],
                    'message' => $translation['message']
                ]
            );
        }

        return redirect()->route('notification-templates.index')
            ->with('success', 'Notification template updated successfully');
    }
        public function destroy($id)
        {
            $template = NotificationTemplate::findOrFail($id);
                $template->delete();
                 return redirect()->route('notification-templates.index')
            ->with('success', 'Notification Deleted successfully');
        }
}
