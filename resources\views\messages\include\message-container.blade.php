@php
    $previousDate = null;
    $today = \Carbon\Carbon::today();
    $yesterday = \Carbon\Carbon::yesterday();
@endphp

{{-- Loading indicator for scroll-up pagination --}}
@if ($hasMorePages ?? false)
    <div id="indicator" 
         data-load-more-msg="{{ translate('chatbox.load_more_msg_txt') }}" 
         class="load-more-messages-indicator" 
         data-has-more="true" 
         data-next-page="{{ ($currentPage ?? 1) + 1 }}" 
         style="display: none;">
        <div class="text-center py-2">
            <div class="spinner-border spinner-border-sm" role="status">
                <span class="visually-hidden">{{ translate('chatbox.load_more_msg_txt') }}</span>
            </div>
            <small class="text-muted ms-2">{{ translate('chatbox.load_more_msg_txt') }}</small>
        </div>
    </div>
@else
    <div id="indicator" 
         class="load-more-messages-indicator" 
         data-has-more="false" 
         style="display: none;">
        <div class="text-center py-2">
            <small class="text-muted">{{ translate('chatbox.no_more_msg_txt') }}</small>
        </div>
    </div>
@endif



@forelse ($messages as $message)
    @php
        $messageDate = \Carbon\Carbon::parse($message->created_at);
        $currentDateString = $messageDate->format('Y-m-d');

        // Format date display
        if ($messageDate->isSameDay($today)) {
            $dateDisplay = 'Today';
        } elseif ($messageDate->isSameDay($yesterday)) {
            $dateDisplay = 'Yesterday';
        } else {
            $dateDisplay = $messageDate->format('M j, Y');
        }
    @endphp

    {{-- Show date separator when date changes --}}
    @if ($previousDate !== $currentDateString)
        <div class="chat_date_time">
            <p>{{ $dateDisplay }}</p>
        </div>
        @php
            $previousDate = $currentDateString;
        @endphp
    @endif

    @if ($message->sender_id == auth()->id())
        <div class="msg msg_right" data-message-id="{{ $message->id }}">
            <div class="chat_bubble">
                @if ($message->message_type === 'text')
                    <p class="">{{ htmlspecialchars_decode($message->content, ENT_QUOTES) }}</p>
                @elseif ($message->message_type === 'image' && $message->attachment)
                    @php
                        $attachment = json_decode($message->attachment, true);
                    @endphp
                    <div class="message-attachment">
                        <img src="{{ $attachment['file_url'] }}" alt="{{ $attachment['original_name'] }}"
                             style="max-width: 200px; max-height: 200px; border-radius: 8px; cursor: pointer;"
                             onclick="window.open('{{ $attachment['file_url'] }}', '_blank')">
                        @if($message->content && $message->content !== 'Sent an image')
                            <p class="">{{ htmlspecialchars_decode($message->content, ENT_QUOTES) }}</p>
                        @endif
                    </div>
                @else
                    <p class="">{{ htmlspecialchars_decode($message->content, ENT_QUOTES) }}</p>
                @endif
                <span class="message-time">{{ $messageDate->format('g:i A') }}</span>
                @if($message->seen_by_user)
                    <div class="message-seen-status">
                        <small class="text-muted">Seen by {{ $message->seen_by_user->name }}</small>
                    </div>
                @endif
            </div>
        </div>
    @else
        <div class="msg msg_left" data-message-id="{{ $message->id }}">
            <div class="chat_bubble">
                @if ($message->message_type === 'text')
                    <p class="">{{ htmlspecialchars_decode($message->content, ENT_QUOTES) }}</p>
                @elseif ($message->message_type === 'image' && $message->attachment)
                    @php
                        $attachment = json_decode($message->attachment, true);
                    @endphp
                    <div class="message-attachment">
                        <img src="{{ $attachment['file_url'] }}" alt="{{ $attachment['original_name'] }}"
                             style="max-width: 200px; max-height: 200px; border-radius: 8px; cursor: pointer;"
                             onclick="window.open('{{ $attachment['file_url'] }}', '_blank')">
                        @if($message->content && $message->content !== 'Sent an image')
                            <p class="">{{ htmlspecialchars_decode($message->content, ENT_QUOTES) }}</p>
                        @endif
                    </div>
                @else
                    <p class="">{{ htmlspecialchars_decode($message->content, ENT_QUOTES) }}</p>
                @endif
                <span class="message-time">{{ $messageDate->format('g:i A') }}</span>
                @if($message->seen_by_user)
                    <div class="message-seen-status">
                        <small class="text-muted">Seen by {{ $message->seen_by_user->name }}</small>
                    </div>
                @endif
            </div>
        </div>
    @endif
@empty
    <div class="no-messages-placeholder" style="text-align: center; padding: 40px 20px; color: #666;">
        <i class="fas fa-comments" style="font-size: 48px; margin-bottom: 16px; opacity: 0.5;"></i>
        <h5 style="margin-bottom: 8px;">{{ translate('chatbox.start_conversation_txt') }}</h5>
        <p style="margin: 0; font-size: 14px;">{{ translate('chatbox.begin_chat_txt') }}</p>
    </div>
@endforelse
