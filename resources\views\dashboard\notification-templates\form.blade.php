@push('css')
<style>
    #languageTabs {
        border-bottom: 2px solid #f0f0f0;
        margin-bottom: 0;
    }

    #languageTabs li {
        margin-bottom: -2px;
    }

    #languageTabs li a {
        background-color: #f8f9fa;
        color: #6c757d;
        border: 1px solid #dee2e6;
        border-radius: 8px 8px 0 0;
        padding: 12px 24px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    #languageTabs li a:hover {
        background-color: #e9ecef;
        color: #495057;
        text-decoration: none;
    }

    #languageTabs li.active a,
    #languageTabs li.active a:hover,
    #languageTabs li.active a:focus {
        background-color: #ffce32;
        color: #000;
        border-color: #ffce32;
        border-radius: 8px 8px 0 0;
    }

    .tab-content {
        padding: 20px 0;
    }

    .form_field_padding {
        margin-bottom: 20px;
    }

    .form_field_padding label {
        font-weight: 600;
        margin-bottom: 8px;
        color: #333;
    }

    .form-control {
        border: 1px solid #ddd;
        border-radius: 6px;
        padding: 12px 15px;
        font-size: 14px;
    }

    .form-control:focus {
        border-color: #ffce32;
        box-shadow: 0 0 0 0.2rem rgba(255, 206, 50, 0.25);
    }

    .btn.create_btn {
        background-color: #ffce32;
        border: none;
        color: #000;
        padding: 12px 30px;
        border-radius: 6px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn.create_btn:hover {
        background-color: #e6b82e;
        transform: translateY(-1px);
    }

    .help-block {
        color: #dc3545;
        font-size: 12px;
        margin-top: 5px;
    }

    .has-error .form-control {
        border-color: #dc3545;
    }

    .notification-key-field {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        padding: 15px;
        border-radius: 6px;
        margin-bottom: 20px;
    }

    .available-placeholders {
        background-color: #e7f3ff;
        border: 1px solid #b3d9ff;
        padding: 15px;
        border-radius: 6px;
        margin-top: 10px;
    }

    .placeholder-tag {
        background-color: #007bff;
        color: white;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 11px;
        margin: 2px;
        display: inline-block;
    }
</style>
@endpush

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="white_card card_height_100 mb_30">
                <div class="white_card_header">
                    <div class="box_header m-0">
                        <div class="main-title">
                            <h3 class="m-0">{{ isset($template) ? 'Edit' : 'Create' }} Notification Template</h3>
                        </div>
                        <div class="header_more_tool">
                            <a href="{{ route('notification-templates.index') }}" class="btn btn-secondary">
                                <i class="fa fa-arrow-left"></i> Back
                            </a>
                        </div>
                    </div>
                </div>
                <div class="white_card_body">
                    <form action="{{ isset($template) ? route('notification-templates.update', $template->id) : route('notification-templates.store') }}" 
                          method="POST">
                        @csrf
                        @if(isset($template))
                            @method('PUT')
                        @endif

                        <div id="inputFormRow">
                            <!-- Notification Key Field -->
                            <div class="notification-key-field">
                                <div class="form_field_padding {{ $errors->has('key') ? 'has-error' : '' }}">
                                    <label for="key">Notification Key *</label>
                                    <input type="text" 
                                           class="form-control" 
                                           name="key" 
                                           id="key"
                                           value="{{ old('key', $template->key ?? '') }}" 
                                           placeholder="e.g., booking_placed, user_registered"
                                           {{ isset($template) && $template->type === 'system' ? 'readonly' : '' }}>
                                    {!! $errors->first('key', '<p class="help-block">:message</p>') !!}
                                    <small class="text-muted">
                                        This key will be used in your code to reference this notification template.
                                    </small>
                                </div>

                                @if(!isset($template))
                                <div class="form_field_padding">
                                    <label for="type">Template Type</label>
                                    <select class="form-control" name="type" id="type">
                                        <option value="custom">Custom</option>
                                        <option value="system">System</option>
                                    </select>
                                </div>
                                @endif
                            </div>

                            <!-- Placeholders Field -->
                            <div class="form_field_padding">
                                <label for="placeholders">Available Placeholders</label>
                                <input type="text" 
                                       class="form-control" 
                                       name="placeholders_input" 
                                       id="placeholders_input"
                                       value="{{ isset($template) ? implode(', ', $template->getPlaceholdersList()) : '' }}" 
                                       placeholder="customer_name, listing_name, check_in_date">
                                <small class="text-muted">
                                    Enter placeholder names separated by commas (without colons)
                                </small>
                            </div>

                            <!-- Language Tabs -->
                            <ul class="nav nav-tabs" id="languageTabs" role="tablist">
                                <li role="presentation" class="active">
                                    <a href="#en" aria-controls="en" role="tab" data-toggle="tab">English</a>
                                </li>
                                <li role="presentation">
                                    <a href="#es" aria-controls="es" role="tab" data-toggle="tab">Español</a>
                                </li>
                            </ul>

                            <!-- Tab Content -->
                            <div class="tab-content" id="languageTabContent">
                                <!-- English Tab -->
                                <div role="tabpanel" class="tab-pane active" id="en">
                                    <div class="form_field_padding {{ $errors->has('translations.en.title') ? 'has-error' : '' }}">
                                        <label for="title_en">Title (English) *</label>
                                        <input type="text" 
                                               class="form-control" 
                                               name="translations[en][title]" 
                                               id="title_en"
                                               value="{{ old('translations.en.title', isset($template) ? $template->getTranslation('en', 'title') : '') }}"
                                               placeholder="E.g., Booking Confirmation">
                                        {!! $errors->first('translations.en.title', '<p class="help-block">:message</p>') !!}
                                    </div>
                                    <div class="form_field_padding {{ $errors->has('translations.en.message') ? 'has-error' : '' }}">
                                        <label for="message_en">Message (English) *</label>
                                        <textarea class="form-control" 
                                                  name="translations[en][message]" 
                                                  id="message_en"
                                                  rows="4"
                                                  placeholder="Your booking has been confirmed...">{{ old('translations.en.message', isset($template) ? $template->getTranslation('en', 'message') : '') }}</textarea>
                                        {!! $errors->first('translations.en.message', '<p class="help-block">:message</p>') !!}
                                        
                                        <div class="available-placeholders">
                                            <strong>Available Placeholders:</strong><br>
                                            @if(isset($template) && $template->placeholders)
                                                @foreach($template->getPlaceholdersList() as $placeholder)
                                                    <span class="placeholder-tag">:{{ $placeholder }}</span>
                                                @endforeach
                                            @else
                                                <span class="placeholder-tag">:customer_name</span>
                                                <span class="placeholder-tag">:listing_name</span>
                                                <!-- default placeholders -->
                                            @endif
                                        </div>
                                    </div>
                                </div>

                                <!-- Spanish Tab -->
                                <div role="tabpanel" class="tab-pane" id="es">
                                    <div class="form_field_padding {{ $errors->has('translations.es.title') ? 'has-error' : '' }}">
                                        <label for="title_es">Title (Español) *</label>
                                        <input type="text" 
                                               class="form-control" 
                                               name="translations[es][title]" 
                                               id="title_es"
                                               value="{{ old('translations.es.title',isset($template) ? $template->getTranslation('es', 'title') : '') }}"
                                               placeholder="E.g., Confirmación de Reserva">
                                        {!! $errors->first('translations.es.title', '<p class="help-block">:message</p>') !!}
                                    </div>
                                    <div class="form_field_padding {{ $errors->has('translations.es.message') ? 'has-error' : '' }}">
                                        <label for="message_es">Message (Español) *</label>
                                        <textarea class="form-control" 
                                                  name="translations[es][message]" 
                                                  id="message_es"
                                                  rows="4"
                                                  placeholder="Su reserva ha sido confirmada...">{{ old('translations.es.message',isset($template) ? $template->getTranslation('es', 'message') : '') }}</textarea>
                                        {!! $errors->first('translations.es.message', '<p class="help-block">:message</p>') !!}
                                        
                                        <div class="available-placeholders">
                                            <strong>Marcadores Disponibles:</strong><br>
                                            @if(isset($template) && $template->placeholders)
                                                @foreach($template->getPlaceholdersList() as $placeholder)
                                                    <span class="placeholder-tag">:{{ $placeholder }}</span>
                                                @endforeach
                                            @else
                                                <span class="placeholder-tag">:customer_name</span>
                                                <span class="placeholder-tag">:listing_name</span>
                                                <!-- default placeholders -->
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form_field_padding">
                                <div class="list_form_btn">
                                    <input class="btn create_btn" type="submit" value="{{ isset($template) ? 'Update Template' : 'Create Template' }}">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
