<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group.
|
*/

use App\Http\Controllers\{
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>ing<PERSON><PERSON>roller,
    <PERSON>t<PERSON><PERSON>roller,
    ContactController,
    CronController,
    <PERSON><PERSON><PERSON><PERSON>roller,
    LanguageController,
    MessagesController,
    NotificationTemplateController,
    ProfileController,
    ReviewController,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>listController
};
use App\Http\Controllers\ShuftiVerificationController;
use App\Models\NotificationTemplate;
use App\Models\NotificationTemplateTranslation;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Public Routes (No Authentication Required)
|--------------------------------------------------------------------------
*/

// ===== UTILITY & SYSTEM ROUTES =====
Route::get('update-conversion-rates-list', [WebsiteController::class, 'updateConversionRatesList']);
Route::get('/clear-all', [WebsiteController::class, 'cache_clear']);
Route::get('google-map', [WebsiteController::class, 'proxy_google']);
Route::post('/translate-text', [WebsiteController::class, 'translate']);
Route::post('/translate-dynamic', [WebsiteController::class, 'translateDynamic'])->name('translate.dynamic');

// ===== LISTING & SEARCH ROUTES =====
Route::get('search/{location_slug}', [WebsiteController::class, 'locationSearch'])->name('location_search');
Route::get('url/{url_code}', [WebsiteController::class, 'redirectToListing'])->name('redirectToListing');
Route::get('get-listing', [WebsiteController::class, 'get_listing'])->name('get_listing')->middleware(['ajax', 'cacheControl']);
Route::get('listing-search-form', [WebsiteController::class, 'listing_search_form'])->name('listing_search_form');
Route::get('listing-search', [WebsiteController::class, 'listing_search'])->name('listing_search');
// Route::get('detail/{listing_id}/{slug}', [WebsiteController::class, 'listing_detail'])->name('detail');

// ===== LOCALIZATION & CURRENCY ROUTES =====
Route::get('locale/{lang}', [WebsiteController::class, 'locale'])->name('locale')->where('lang', 'en|es');
Route::get('currency/{to}', [WebsiteController::class, 'currency'])->name('currency');

// ===== STATIC PAGES =====
// Route::get('contact-us', [WebsiteController::class, 'contactUs'])->name('contact_us');
// Route::get('about', [WebsiteController::class, 'about'])->name('about');
// Route::get('privacy_policy', [WebsiteController::class, 'privacy'])->name('privacy_policy');
// Route::get('supplier-aggreement', [WebsiteController::class, 'supplier_aggreement'])->name('supplier_aggreement');
// Route::get('terms', [WebsiteController::class, 'terms'])->name('terms');

// ===== FAQ & HELP CENTER =====
// Route::get('faq', [WebsiteController::class, 'faq'])->name('faq');
// Route::get('faq/{slug}', [WebsiteController::class, 'faq_detail'])->name('faq_detail');
// Route::get('help-center', [WebsiteController::class, 'helpCenter'])->name('help_center');
// Route::get('help-center/{slug}', [WebsiteController::class, 'help_center_detail'])->name('help_center_detail');

// ===== BOOKING DETAIL ROUTES =====
Route::get('booking-pdf/{ids}/{booking_number}', [BookingController::class, 'bookingPDF'])->name('my-booking-pdf');
Route::get('cancellation-policy-timeline/{policy_type}/{start}', [WebsiteController::class, 'cancellationPolicyTimeline'])->name('cancellation_policy_timeline');

// ===== AJAX CALCULATION ROUTES =====
Route::get('calculate-detail-daily', [WebsiteController::class, 'calculate_detail_daily'])->name('calculate_detail_daily')->middleware('ajax');
Route::get('calculate-detail-hourly', [WebsiteController::class, 'calculate_detail_hourly'])->name('calculate_detail_hourly')->middleware('ajax');
Route::get('calculate-detail-tour', [WebsiteController::class, 'calculate_detail_tour'])->name('calculate_detail_tour')->middleware('ajax');



// ===== USER INFO & MISC =====
Route::get('/user-info', [WebsiteController::class, 'getUserInfo']);
Route::get('dummy-details', [WebsiteController::class, 'dummyDetails'])->name('dummy-details');

// ===== AUTHENTICATION ROUTES =====
Route::post('signup', [AuthController::class, 'sign_up'])->name('sign_up');
Route::post('forget-password', [AuthController::class, 'forget_password'])->name('forget_password');
Route::post('reset-password', [AuthController::class, 'reset_password'])->name('reset_password');

// ===== OTP VERIFICATION ROUTES =====
Route::post('resend-otp', [AuthController::class, 'resend_otp'])->name('resend_otp')->middleware('throttle:1,0.1');
Route::post('resend-otp-kyc', [AuthController::class, 'resend_otp_kyc'])->name('resend_otp_kyc')->middleware('throttle:1,0.1');
Route::post('check-otp', [AuthController::class, 'check_otp'])->name('check_otp')->middleware('throttle:1,0.1');
Route::post('check-email-otp', [AuthController::class, 'check_email_otp'])->name('check_email_otp');
Route::post('check-phone-otp', [AuthController::class, 'check_phone_otp'])->name('check_phone_otp');
Route::post('check-kyc-phone-otp', [AuthController::class, 'check_kyc_phone_otp'])->name('check_kyc_phone_otp');

// ===== EMAIL & PHONE VERIFICATION ROUTES =====
Route::post('sign-up-email-verification', [AuthController::class, 'signUpEmailVerification'])->name('signUpEmailVerification');
Route::post('sign-up-phone-verification', [AuthController::class, 'signUpPhoneVerification'])->name('signUpMessageVerification');
Route::post('wallet-verification', [AuthController::class, 'WalletVerification'])->name('WalletVerification');
Route::post('change-email', [AuthController::class, 'change_email'])->name('change_email');
Route::post('update-phone-verified-status', [AuthController::class, 'updatePhoneVerifiedStatus'])->name('update_phone_verified_status');

// ===== WEBHOOK ROUTES =====
Route::post('stripe-webhook', [StripeIdentityController::class, 'handleWebhook']);
Route::get('tipalti-payee-by-id/{payee_id}', [StripeIdentityController::class, 'GetPayeeById']);
Route::post('/webhook/payment-intent', [StripeIdentityController::class, 'paymentStatus']);

// Tipalti Webhook Routes
Route::post('payment-deferred', [WebhookController::class, 'paymentDeferred']);
Route::post('payment-cancelled', [WebhookController::class, 'paymentCancelled']);
Route::post('payee-amount-submitted', [WebhookController::class, 'payeeSubmitted']);
Route::post('payee-detail-changed', [WebhookController::class, 'payeeDetailChanged']);

// ===== CONTACT FORM =====
Route::post('contact-form', [ContactController::class, 'create'])->name('contact_form');
Route::post('/set-locale', [LanguageController::class, 'setLocale'])->name('set-locale');

/*
|--------------------------------------------------------------------------
| Authenticated Routes (Requires Authentication)
|--------------------------------------------------------------------------
*/

Route::group(['middleware' => ['auth', 'roles', 'last_active'], 'roles' => ['admin', 'sub_admin', 'user', 'service', 'customer']], function () {

    // ===== BOOKING MANAGEMENT =====
    Route::get('past-booking', [WebsiteController::class, 'get_past_booking'])->name('get_past_booking');
    Route::get('current-booking', [WebsiteController::class, 'get_current_booking'])->name('get_current_booking');

    // ===== REVIEW MANAGEMENT =====
    Route::get('review-get/{listing_id}', [ReviewController::class, 'review_get'])->name('review_get')->middleware('ajax');
    Route::post('review-post', [ReviewController::class, 'review_post'])->name('review_post');
    Route::delete('review-image/{id}', [ReviewController::class, 'deleteReviewImage'])->name('review_image_delete');
    Route::get('get-review-form', [ReviewController::class, 'get_review_form'])->name('get_review_form')->middleware('ajax');
    Route::get('edit-review-form/{id}', [ReviewController::class, 'edit_review_form'])->name('edit_review_form')->middleware('ajax');
    Route::get('view-review-details', [ReviewController::class, 'view_review_details'])->name('view_review_details')->middleware('ajax');
    Route::get('edit-review-details/{id}', [ReviewController::class, 'edit_review_details'])->name('edit_review_details')->middleware('ajax');

    // ===== ROLE SWITCHING =====
    Route::get('list-your-asset', [WebsiteController::class, 'list_asset'])->name('list_asset');
    Route::get('browse-listing', [WebsiteController::class, 'browse_listing'])->name('browse_listing');

    // ===== STRIPE VERIFICATION & PAYMENT CARDS =====
    Route::get('/account-setting/stripe', [StripeIdentityController::class, 'stripeCustomPage'])->name('stripe_custom_page')->withoutMiddleware('kyc_verification');
    Route::get('delete-card/{card_id}', [StripeIdentityController::class, 'delete_card'])->name('cards.delete');
    Route::get('default-card/{card_id}', [StripeIdentityController::class, 'default_card'])->name('cards.default');

    // ===== WISHLIST MANAGEMENT =====
    Route::post('add-wishlist', [WishlistController::class, 'add_wishlist'])->name('add_wishlist');
    Route::get('delete-wishlist/{id}', [WishlistController::class, 'wishlist_delete']);

    // ===== CART MANAGEMENT =====
    Route::post('cart', [CartController::class, 'add_cart'])->name('add_cart');
    // Route::get('cart', [CartController::class, 'cart_load'])->name('cart');
    // Route::delete('delete-cart/{cart_id}', [CartController::class, 'delete_cart'])->name('delete_cart');
    // Route::get('checkout', [CartController::class, 'checkout'])->name('checkout');
    // Route::post('checkout', [CartController::class, 'checkout_post'])->name('checkout.post');

    // ===== RESERVATION =====
    Route::post('reserve', [BookingController::class, 'reserve_data'])->name('reserve_data');

    // ===== TIPALTI PAYMENT INTEGRATION =====
    Route::get('/tipalti/auth', [TipaltiAuthController::class, 'redirectToAuthorization'])->name('tipalti.auth');
    Route::get('/tipalti/callback', [TipaltiAuthController::class, 'handleCallback'])->name('tipalti.callback');
    Route::get('/tipalti/refresh-token', [TipaltiAuthController::class, 'refreshAccessToken'])->name('tipalti.refresh');
    Route::post('/tipalti/create-payee', [TipaltiAuthController::class, 'createPayee'])->name('tipalti.createPayee');
    Route::get('/tipalti/iframe/{payeeId}', [TipaltiAuthController::class, 'showIframe'])->name('tipalti.showIframe');

    // ===== BOOKING CONFIRMATION & MANAGEMENT =====
    Route::post('confirm-booking/{listing_ids}', [BookingController::class, 'confirm_booking_add'])->name('confirm_booking_add');
    Route::get('tour-update-booking/{listing_ids}', [BookingController::class, 'tour_update_booking'])->name('tour_update_booking');
    Route::get('cancel-booking/{booking_id}', [BookingController::class, 'cancelBooking'])->name('cancel_booking');
    Route::get('booking-generate-pdf/{booking_id}', [BookingController::class, 'generate_booking_pdf'])->name('generate_booking_pdf');

    // ===== PAYPAL PAYMENT ROUTES =====
    Route::post('/paypal/create-order', [BookingController::class, 'processAdvPayPalPayment']);
    Route::get('paypal-success', [BookingController::class, 'paypalSuccess'])->name('paypal.success');
    Route::get('paypal-cancel', [BookingController::class, 'paypalCancel'])->name('paypal.cancel');

    // ===== REPORTING =====
    Route::post('report-form', [WebsiteController::class, 'report_form'])->name('report_form');
    Route::post('report-review', [WebsiteController::class, 'report_review'])->name('report_review');
    Route::post('report-listing', [WebsiteController::class, 'report_listing'])->name('report_listing');

    // ===== JWT TOKEN =====
    Route::get('get-bearer-token', [JWTController::class, 'getBearerToken'])->name('getBearerToken')->middleware('ajax');

    // ===== PROFILE MANAGEMENT =====
    Route::post('update-password', [ProfileController::class, 'update_password'])->name('userprofile.pass_update');
    Route::post('profile-update', [ProfileController::class, 'profile_update'])->name('userprofile.update');
    Route::post('profile-img-update', [ProfileController::class, 'profile_img_update'])->name('userprofile.profile_img');

    // ===== SESSION MANAGEMENT =====
    Route::post('session-logout', [ProfileController::class, 'logout_session'])->name('session.logout');

    // ===== TESTING ROUTES =====
    Route::get('testing_email_temp', [WebsiteController::class, 'testingEmailTemp'])->name('testing_email_temp');

    // ===== MESSAGING SYSTEM =====
    Route::get('inboxChat/{provider_ids}', [WebsiteController::class, 'inboxChat'])->name('inboxChat');

    Route::prefix('message')->group(function () {
        Route::get('me/{conversation_ids?}', [MessagesController::class, 'index'])->name('message.index');
        Route::get('dashboard_messenger', [MessagesController::class, 'dashboardMessenger'])->name('message.dashboard_messenger');
        Route::get('/get-conversations', [MessagesController::class, 'get_conversations'])->name('message.get_conversations');
        Route::get('/fetch-conversation/{type}', [MessagesController::class, 'fetch_conversation'])->name('message.fetch_conversation')->where('type', 'private|support');
        Route::get('/parse-message/{conversation_ids}', [MessagesController::class, 'parse_message'])->name('message.parse_message')->where('type', 'private|support');
        Route::get('/search-messages/{conversation_ids}', [MessagesController::class, 'search_messages'])->name('message.search_messages');
        Route::get('/load-more-messages/{conversation_ids}', [MessagesController::class, 'load_more_messages'])->name('message.load_more_messages');
        Route::post('/send-message/{conversation_id}', [MessagesController::class, 'send_message'])->name('message.send_message');
        Route::post('/typing/{conversation_id}', [MessagesController::class, 'typing'])->name('message.typing');
        Route::post('/mark-as-read/{conversation_id}', [MessagesController::class, 'markAsRead'])->name('message.mark_as_read');
    });

    // ===== KYC (Know Your Customer) Verification Routes =====
    Route::prefix('kyc')->name('kyc.')->group(function () {
        // Main KYC routesset-locale
        Route::get('/', [ShuftiVerificationController::class, 'index'])->name('index');
        // Start verification routes
        Route::get('/start/eidv', [ShuftiVerificationController::class, 'startEIDV'])->name('start.eidv');
        Route::get('/start/kyc', [ShuftiVerificationController::class, 'startKYC'])->name('start.kyc');
        // Status and success routes
        Route::get('/status', [ShuftiVerificationController::class, 'getStatus'])->name('status');
        Route::get('/success', [ShuftiVerificationController::class, 'success'])->name('success');

        // Restart verification
        Route::get('/restart/{type}', [ShuftiVerificationController::class, 'restart'])->name('restart');
    });

    // ===== Account Deletion Routes =====
    Route::get('/check-active-bookings', [AuthController::class, 'checkActiveBookings'])->name('check_active_bookings');
    Route::post('/verify-delete-password', [AuthController::class, 'verifyDeletePassword'])->name('verify_delete_password');
    Route::post('/verify-delete-otp', [AuthController::class, 'verifyDeleteOtp'])->name('verify_delete_otp');
    Route::post('/delete-account', [AuthController::class, 'deleteAccount'])->name('delete_account');
});

/*
|--------------------------------------------------------------------------
| Social Authentication Routes
|--------------------------------------------------------------------------
*/

Route::get('auth/{provider}/', 'Auth\SocialLoginController@redirectToProvider');
Route::get('{provider}/callback', 'Auth\SocialLoginController@handleProviderCallback');
Route::get('logout', 'Auth\LoginController@logout');

/*
|--------------------------------------------------------------------------
| Laravel Default Auth Routes
|--------------------------------------------------------------------------
*/

Auth::routes();

/*
|--------------------------------------------------------------------------
| Additional Cron Job Routes
|--------------------------------------------------------------------------
*/

Route::get('delete-draft-booking', [CronController::class, 'delete_draft_listing']);

// ===== CRON JOB ROUTES =====
Route::get('complete-job-cron', [CronController::class, 'index']);
Route::get('complete-wallet-job-cron', [CronController::class, 'walletAmount']);
Route::get('cancelled-wallet-job-cron', [CronController::class, 'cancellationAmount']);
Route::get('scheduled-wallet-job-cron', [CronController::class, 'scheduledAmount']);

/*
|--------------------------------------------------------------------------
| Testing & Development Routes
|--------------------------------------------------------------------------
*/

// Route::get('testing-database', function () {
//     // return false;
//     $users = App\Models\ContactInfo::get();
//     foreach ($users as $user) {
//         $user->ids = \Illuminate\Support\Str::uuid();
//         $user->save();
//     }
//     return "uuid stored";
// });

/*
|--------------------------------------------------------------------------
| Localization Routes (Must be at the end)
|--------------------------------------------------------------------------
*/

// // Redirect root to default locale
// Route::get('/', function () {
//     $locale = session('locale', 'en'); // Default to 'en' if no locale in session
//     return redirect("/{$locale}");
// });

// // Localized home routes - placed at the end to avoid conflicts with other routes
// Route::get('/{locale}', [WebsiteController::class, 'index'])
//     ->name('home')
//     ->where('locale', 'en|es')
//     ->middleware('setLocale');
// Route::redirect('/', '/' . session('locale', 'en'));
Route::get("/", function(){
    return redirect("/". session("locale", "en"));
});

Route::group(['prefix' => '{locale}', 'where' => ['locale' => 'en|es'], 'middleware' => 'setLocale'], function () {

    // ===== LISTING & DETAIL PAGE (LOCALIZED) =====
    Route::get('/', [WebsiteController::class, 'index'])->name('index');
    Route::get('detail/{listing_id}/{slug}', [WebsiteController::class, 'listing_detail'])->name('detail');

    // ===== AUTHENTICATED ROUTES (LOCALIZED) =====
    Route::middleware('auth')->group(function () {

        // ===== BOOKINGS (LOCALIZED) =====
        Route::get('confirm-booking', [BookingController::class, 'confirm_booking'])->name('confirm_booking');
        Route::get('bookings', [WebsiteController::class, 'bookings'])->name('bookings');
        Route::get('booking-detail/{ids}/{booking_number}', [BookingController::class, 'bookingDetail'])->name('my-booking-detail');


        // ===== WISHLIST (LOCALIZED) ===== 
        Route::get('wishlist', [WishlistController::class, 'wishlist_load'])->name('wishlist');

        // ===== ACCOUNT SETTINGS (LOCALIZED) =====
        Route::get('/account-setting', [StripeIdentityController::class, 'initiateVerification'])->name('webaccount_setting')->withoutMiddleware('kyc_verification');
    });


    // ===== STATIC PAGES (LOCALIZED) =====
    Route::get('contact-us', [WebsiteController::class, 'contactUs'])->name('contact_us');
    Route::get('who_we_are', [WebsiteController::class, 'about'])->name('about');
    Route::get('privacy_policy', [WebsiteController::class, 'privacy'])->name('privacy_policy');
    Route::get('supplier-aggreement', [WebsiteController::class, 'supplier_aggreement'])->name('supplier_aggreement');
    Route::get('terms', [WebsiteController::class, 'terms'])->name('terms');

    // ===== FAQ & HELP CENTER (LOCALIZED) =====
    Route::get('faq', [WebsiteController::class, 'faq'])->name('faq');
    Route::get('faq/{slug}', [WebsiteController::class, 'faq_detail'])->name('faq_detail');
    Route::get('help-center', [WebsiteController::class, 'helpCenter'])->name('help_center');
    Route::get('help-center/{slug}', [WebsiteController::class, 'help_center_detail'])->name('help_center_detail');


    // ===== KYC VERIFICATION (LOCALIZED) =====
    Route::get('kyc', [ShuftiVerificationController::class, 'index'])->name('kyc.index');
    Route::get('kyc/start/eidv', [ShuftiVerificationController::class, 'startEIDV'])->name('kyc.start.eidv');
    Route::get('kyc/start/kyc', [ShuftiVerificationController::class, 'startKYC'])->name('kyc.start.kyc');
    Route::get('kyc/status', [ShuftiVerificationController::class, 'getStatus'])->name('kyc.status');
    Route::get('kyc/success', [ShuftiVerificationController::class, 'success'])->name('kyc.success');
    Route::get('kyc/restart/{type}', [ShuftiVerificationController::class, 'restart'])->name('kyc.restart');


    // Locale download 
    Route::get('download-locale', [WebsiteController::class, 'download_locale'])->name('download_locale');
});

//  Route::prefix('kyc')->name('kyc.')->group(function () {
//         // Main KYC routesset-locale
//         Route::get('/', [ShuftiVerificationController::class, 'index'])->name('index');

//         // Start verification routes
//         Route::get('/start/eidv', [ShuftiVerificationController::class, 'startEIDV'])->name('start.eidv');
//         Route::get('/start/kyc', [ShuftiVerificationController::class, 'startKYC'])->name('start.kyc');

//         // Status and success routes
//         Route::get('/status', [ShuftiVerificationController::class, 'getStatus'])->name('status');
//         Route::get('/success', [ShuftiVerificationController::class, 'success'])->name('success');

//         // Restart verification
//         Route::get('/restart/{type}', [ShuftiVerificationController::class, 'restart'])->name('restart');
//     });




// Callback route (no auth required - webhook from Shufti Pro)
Route::post('/kyc/callback', [ShuftiVerificationController::class, 'handleCallback'])->name('kyc.callback');

Route::group(['middleware' => ['auth', 'roles', 'last_active'], 'roles' => ['user']], function () {

    // Notification Templates
    Route::get('/notification-templates', [NotificationTemplateController::class, 'index'])->name('notification-templates.index');
    Route::get('/notification-templates/create', [NotificationTemplateController::class, 'create'])->name('notification-templates.create');
    Route::post('/notification-templates', [NotificationTemplateController::class, 'store'])->name('notification-templates.store');
    Route::get('/notification-templates/{id}/edit', [NotificationTemplateController::class, 'edit'])->name('notification-templates.edit');
    Route::put('/notification-templates/{id}', [NotificationTemplateController::class, 'update'])->name('notification-templates.update');
    Route::delete('/notification-templates/{id}', [NotificationTemplateController::class, 'destroy'])->name('notification-templates.destroy');
});

Route::get('notification-seeder',function(){
    
        $templates = [
            [
                'key' => 'booking_placed',
                'placeholders' => ['customer_name', 'listing_name', 'check_in_date', 'check_out_date', 'total_amount', 'booking_id'],
                'translations' => [
                    'en' => [
                        'title' => 'Booking Placed',
                        'message' => ':customer_name booked the :listing_name on :check_in_date'
                    ],
                    'es' => [
                        'title' => 'Reserva Realizada',
                        'message' => ':customer_name reservó :listing_name para el :check_in_date'
                    ]
                ]
            ],
            [
                'key' => 'booking_cancelled',
                'placeholders' => ['booking_id'],
                'translations' => [
                    'en' => [
                        'title' => 'Booking Cancelled',
                        'message' => ':customer_name cancelled booking for :listing_name on :check_in_date'
                    ],
                    'es' => [
                        'title' => 'Reserva Cancelada',
                        'message' => ':customer_name canceló la reserva de :listing_name para el :check_in_date'
                    ]
                ]
            ],
            [
                'key' => 'user_registered',
                'placeholders' => ['guest_first_name'],
                'translations' => [
                    'en' => [
                        'title' => 'New User Registered',
                        'message' => 'A new user has registered on the platform'
                    ],
                    'es' => [
                        'title' => 'Nuevo Usuario Registrado',
                        'message' => 'Un nuevo usuario se ha registrado en la plataforma'
                    ]
                ]
            ]
        ];

       foreach ($templates as $templateData) {
            $template = NotificationTemplate::create([
                'key' => $templateData['key'],
                'type' => 'system',
                'is_active' => true,
                'placeholders' => $templateData['placeholders']
            ]);

            foreach ($templateData['translations'] as $locale => $translation) {
                NotificationTemplateTranslation::create([
                    'notification_template_id' => $template->id,
                    'locale' => $locale,
                    'title' => $translation['title'],
                    'message' => $translation['message']
                ]);
            }
        }
});
