<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Message extends Model
{
    use HasFactory;
    protected $fillable = [
        'conversation_id',
        'sender_id',
        'content',
        'message_type',
        'attachment',
        'read_at',
        'is_edited',
        'edited_at'
    ];

    function sender(){
        return $this->belongsTo(User::class, "sender_id");
    }

    function conversation(){
        return $this->belongsTo(Conversation::class, "conversation_id");
    }

    /**
     * Get the user who has seen this message (for private conversations)
     * Returns null if message hasn't been seen by the receiver or if this is not the last seen message
     */
    public function getSeenByUserAttribute()
    {
        // Only show seen status for messages sent by current user
        if ($this->sender_id !== auth()->id()) {
            return null;
        }

        // If message hasn't been read, return null
        if (!$this->read_at) {
            return null;
        }

        // Get the other participant in the conversation
        $conversation = $this->conversation;
        if (!$conversation) {
            return null;
        }

        // Only show "Seen by" on the latest message that has been read
        // Check if this is the latest read message from the current user
        $latestReadMessage = Message::where('conversation_id', $this->conversation_id)
            ->where('sender_id', auth()->id()) // Messages sent by current user
            ->whereNotNull('read_at') // That have been read
            ->latest('read_at') // Get the most recently read one
            ->first();

        // Only show seen status if this is the latest read message
        if (!$latestReadMessage || $latestReadMessage->id !== $this->id) {
            return null;
        }

        // Determine who the receiver is
        $receiverId = $conversation->sender_id === auth()->id()
            ? $conversation->receiver_id
            : $conversation->sender_id;

        // Return the receiver user with caching
        static $userCache = [];
        if (!isset($userCache[$receiverId])) {
            $userCache[$receiverId] = User::find($receiverId);
        }

        return $userCache[$receiverId];
    }

    // setter and geetter for message html decode and encode to prevent <script> tags
    public function setContentAttribute($value)
    {
        $message = htmlspecialchars($value, ENT_QUOTES);
        $this->attributes['content'] = encrypt($message);
    }
    public function getContentAttribute($value)
    {
        try{
            $message = decrypt($value);
        }catch(\Exception $e){
            $message = $value;
        }
        return htmlspecialchars_decode($message, ENT_QUOTES);
    }
}
