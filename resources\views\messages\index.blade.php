@extends('website.layout.master')

@push('css')
    <link rel="stylesheet" href="{{ asset('website/messenger/style.css') }}" />
@endpush

@section('content')
    <section class="messenger_main_sec">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="inner_section_messenger_main_col">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="messenger_wrapper">
                                    <div class="row">
                                        <div class="col-md-3 chat_sidebar_col">
                                            <div class="inner_section_messenger_sidebar">
                                                <div class="sidebar_chats_toggle_buttons_wrapper">
                                                    <ul class="nav nav-pills mb-3" id="pills-tab" role="tablist">

                                                        <li class="nav-item dropdown d-flex align-items-center" role="presentation">
                                                            <button class="nav-link active" id="pills-conversations-tab"
                                                                data-bs-toggle="pill"
                                                                data-bs-target="#pills-conversations"
                                                                type="button"
                                                                role="tab"
                                                                aria-controls="pills-conversations"
                                                                aria-selected="true">
                                                                <span><img src="{{ asset('website') }}/images/chat_all_icon.svg" alt=""></span><span>{{ translate('chatbox.all') }}</span>
                                                            </button>

                                                            <button class="btn dropdown-toggle dropdown-toggle-split ms-1"
                                                                type="button"
                                                                id="conversationDropdown"
                                                                data-bs-toggle="dropdown"
                                                                aria-expanded="false">
                                                            </button>

                                                            <ul class="dropdown-menu" aria-labelledby="conversationDropdown">
                                                                <li><a class="dropdown-item filter-option active" href="#" data-filter="All" data-image="{{ asset('website') }}/images/chat_all_icon.svg"><span><img src="{{ asset('website') }}/images/chat_all_icon.svg" alt=""></span><span>{{ translate('chatbox.all') }}</span></a></li>
                                                                <li><a class="dropdown-item filter-option" href="#" data-filter="Provider" data-image="{{ asset('website') }}/images/chat_provider_icon.svg"><span><img src="{{ asset('website') }}/images/chat_provider_icon.svg" alt=""></span><span>{{ translate('chatbox.provider') }}</span></a></li>
                                                                <li><a class="dropdown-item filter-option" href="#" data-filter="Traveller" data-image="{{ asset('website') }}/images/chat_traveller_icon.svg"><span><img src="{{ asset('website') }}/images/chat_traveller_icon.svg" alt=""></span><span>{{ translate('chatbox.traveller') }}</span></a></li>
                                                                <li><a class="dropdown-item filter-option" href="#" data-filter="Unread" data-image="{{ asset('website') }}/images/chat_unread_icon.svg"><span><img src="{{ asset('website') }}/images/chat_unread_icon.svg" alt=""></span><span>{{ translate('chatbox.unread') }}</span></a></li>
                                                            </ul>
                                                        </li>

                                                        <li class="nav-item" role="presentation">
                                                            <button class="nav-link" id="pills-support-tab"
                                                                data-bs-toggle="pill" data-bs-target="#pills-support"
                                                                type="button" role="tab" aria-controls="pills-support"
                                                                aria-selected="false">{{ translate('chatbox.support') }}</button>
                                                        </li>
                                                        
                                                    </ul>
                                                    <div class="tab-content" id="pills-tabContent">
                                                        <div class="tab-pane fade show active" id="pills-conversations"
                                                            role="tabpanel" aria-labelledby="pills-conversations-tab">
                                                            <div class="inner_section_chat_threads">

                                                                <div class="chat_threads_search">
                                                                    <i class="fas fa-search"></i>
                                                                    <input type="search" id="search-user-input"
                                                                        class="form-control" aria-describedby="emailHelp"
                                                                        placeholder="{{ translate('chatbox.search_placeholder') }}">
                                                                </div>

                                                                {{-- Chat Threads Container --}}
                                                                <div class="chat_threads_wrapper scrollable-section"
                                                                    id="chat-threads-container">

                                                                </div>
                                                                {{-- Chat Threads Container End --}}

                                                            </div>
                                                        </div>
                                                        <div class="tab-pane fade" id="pills-support" role="tabpanel"
                                                            aria-labelledby="pills-support-tab">
                                                            <div class="inner_section_chat_threads">

                                                                <div class="chat_threads_search">
                                                                    <a href="javascript:void(0)"
                                                                        class="btn start_new_convo_btn">{{ translate('chatbox.support_chat_btn') }}</a>
                                                                </div>

                                                                <div class="chat_threads_wrapper scrollable-section"
                                                                    id="support-chat-threads">

                                                                    @for ($i = 0; $i < 10; $i++)
                                                                        <div class="single_chat_thread">
                                                                            <div class="user_profile_picture">
                                                                                <img src="{{ asset('website') }}/images/user1.png"
                                                                                    alt="User Profile">
                                                                            </div>
                                                                            <div class="chat_details_wrapper">
                                                                                <div class="username_date_wrapper">
                                                                                    <div class="user_name">
                                                                                        <h6>{{ translate('chatbox.user_name') }}</h6>
                                                                                    </div>
                                                                                    <div class="chat_date">
                                                                                        <span>12:35 PM</span>
                                                                                    </div>
                                                                                </div>
                                                                                {{-- <div class="open_chat_icon">
                                                                                    <i class="fas fa-chevron-right"></i>
                                                                                </div> --}}
                                                                                <div class="chat_preview">
                                                                                    <p>Thank you very much, I am waiting for
                                                                                        the parcel.</p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    @endfor

                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-9" id="message-container">
                                            <div
                                                style="display: flex; justify-content: center; align-items: center; height: 100%;">
                                               {{ translate('chatbox.select_conversation') }}
                                            </div>

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Emoji Picker -->
    <div id="emoji-picker" class="emoji-picker">
        <div class="emoji-search-container" style="margin-bottom: 10px;">
            {{-- <input type="text" id="emoji-search" placeholder="Search emojis..."
                   style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 5px; font-size: 14px;"> --}}
        </div>
        <div class="emoji-categories">
            <div class="emoji-category active" data-category="smileys" title="Smileys & Emotion">😀</div>
            <div class="emoji-category" data-category="people" title="People & Body">👋</div>
            <div class="emoji-category" data-category="nature" title="Animals & Nature">🌸</div>
            <div class="emoji-category" data-category="food" title="Food & Drink">🍎</div>
            <div class="emoji-category" data-category="activities" title="Activities">⚽</div>
            <div class="emoji-category" data-category="travel" title="Travel & Places">🚗</div>
            <div class="emoji-category" data-category="objects" title="Objects">💡</div>
            <div class="emoji-category" data-category="symbols" title="Symbols">❤️</div>
        </div>
        <div class="emoji-grid" id="emoji-grid">
            <!-- Emojis will be populated by JavaScript -->
        </div>
    </div>
@endsection


@push('js')
    <script src="https://js.pusher.com/8.4.0/pusher.min.js"></script>

    {{-- Audio element for notification sound --}}
    <audio id="notification-audio" preload="auto">
        <source src="{{ asset('website/messenger/notification-audio.wav') }}" type="audio/wav">
    </audio>

    {{-- Pass configuration to JavaScript --}}
    <script>
        // Set messenger configuration in window object for script.js to access
        window.messengerConfig = {
            auth_id: "{{ auth()->user()->ids }}",
            @if($conversation_ids)
            active_conversation_id: "{{ $conversation_ids }}",
            @endif
            routes: {
                getConversations: "{{ route('message.get_conversations') }}",
                fetchConversation: "{{ route('message.fetch_conversation', 'private') }}",
                parseMessage: "{{ route('message.parse_message', 'CONVERSATION_ID') }}",
                loadMoreMessages: "{{ route('message.load_more_messages', 'CONVERSATION_ID') }}",
                messageIndex: "{{ route('message.index', 'CONVERSATION_ID') }}",
                typing: "{{ route('message.typing', 'CONVERSATION_ID') }}",
                searchMessages: "{{ route('message.search_messages', 'CONVERSATION_ID') }}",
                markAsRead: "{{ route('message.mark_as_read', 'CONVERSATION_ID') }}"
            }
        };
    </script>

    {{-- Load the main messenger script --}}
    <script src="{{ asset('website/messenger/script.js') }}"></script>
@endpush
